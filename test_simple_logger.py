#!/usr/bin/env python3
"""
测试简化的日志功能
"""
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_simple_logging():
    """测试简化的日志功能"""
    print("=" * 50)
    print("测试简化的日志功能")
    print("=" * 50)
    
    try:
        # 清理旧的日志文件
        cleanup_old_logs()
        
        # 导入日志模块
        from core.logger import log
        
        print("✅ 成功导入日志模块")
        
        # 测试不同级别的日志
        print("📝 记录各种级别的日志...")
        
        log.debug("这是一条DEBUG日志")
        log.info("这是一条INFO日志")
        log.warning("这是一条WARNING日志")
        log.error("这是一条ERROR日志")
        
        print("✅ 日志记录完成")
        
        # 等待一下确保写入完成
        import time
        time.sleep(1)
        
        # 检查日志文件
        check_log_files()
        
    except Exception as e:
        print(f"❌ 日志测试失败: {e}")
        import traceback
        traceback.print_exc()

def cleanup_old_logs():
    """清理旧的日志文件"""
    today = datetime.now().strftime("%Y%m%d")
    log_files = [
        f"logs/general/app_{today}.log",
        f"logs/debug/debug_{today}.log",
        f"logs/error/error_{today}.log"
    ]
    
    for log_file in log_files:
        full_path = os.path.join(project_root, log_file)
        if os.path.exists(full_path):
            try:
                os.remove(full_path)
                print(f"🗑️ 清理旧日志文件: {log_file}")
            except Exception as e:
                print(f"⚠️ 清理文件失败 {log_file}: {e}")

def check_log_files():
    """检查日志文件"""
    print("\n" + "=" * 30)
    print("检查日志文件")
    print("=" * 30)
    
    today = datetime.now().strftime("%Y%m%d")
    expected_files = {
        "通用日志": f"logs/general/app_{today}.log",
        "调试日志": f"logs/debug/debug_{today}.log",
        "错误日志": f"logs/error/error_{today}.log"
    }
    
    for name, log_file in expected_files.items():
        full_path = os.path.join(project_root, log_file)
        print(f"\n📁 {name} ({log_file}):")
        
        if os.path.exists(full_path):
            size = os.path.getsize(full_path)
            if size > 0:
                print(f"  ✅ 文件存在，大小: {size} 字节")
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        print(f"  📄 内容:")
                        for line in content.split('\n'):
                            if line.strip():
                                print(f"    {line}")
                except Exception as e:
                    print(f"  ❌ 读取文件失败: {e}")
            else:
                print(f"  ⚠️ 文件存在但为空")
        else:
            print(f"  ❌ 文件不存在")

def test_performance():
    """测试日志性能"""
    print("\n" + "=" * 30)
    print("测试日志性能")
    print("=" * 30)
    
    try:
        from core.logger import log
        import time
        
        # 记录大量日志测试性能
        start_time = time.time()
        
        for i in range(50):
            log.info(f"性能测试日志 {i}")
            if i % 10 == 0:
                log.error(f"错误测试日志 {i}")
            if i % 5 == 0:
                log.debug(f"调试测试日志 {i}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 记录50条日志耗时: {duration:.3f}秒")
        print(f"📊 平均每条日志: {duration/50*1000:.2f}毫秒")
        
        # 等待写入完成
        time.sleep(1)
        
        # 检查文件大小
        today = datetime.now().strftime("%Y%m%d")
        files_to_check = [
            f"logs/general/app_{today}.log",
            f"logs/debug/debug_{today}.log",
            f"logs/error/error_{today}.log"
        ]
        
        total_size = 0
        for log_file in files_to_check:
            full_path = os.path.join(project_root, log_file)
            if os.path.exists(full_path):
                size = os.path.getsize(full_path)
                total_size += size
                print(f"📄 {log_file}: {size} 字节")
        
        print(f"📊 总日志大小: {total_size} 字节")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_logging()
    test_performance()
    
    print("\n" + "=" * 50)
    print("简化日志测试完成")
    print("=" * 50)
