#!/usr/bin/env python3
"""
测试超级安全的日志系统
"""
import os
import sys
import threading
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_ultra_safe_logger():
    """测试超级安全的日志系统"""
    print("=" * 50)
    print("测试超级安全的日志系统")
    print("=" * 50)
    
    try:
        # 导入日志模块
        from core.logger import log
        
        print("✅ 成功导入日志模块")
        
        # 测试基本日志记录
        log.info("测试超级安全日志系统 - INFO")
        log.warning("测试超级安全日志系统 - WARNING")
        log.error("测试超级安全日志系统 - ERROR")
        log.debug("测试超级安全日志系统 - DEBUG")
        
        print("✅ 基本日志记录完成")
        
        # 测试多线程环境
        test_multithreading()
        
        # 测试大量日志记录
        test_bulk_logging()
        
        print("✅ 所有测试完成，无缓冲区错误")
        
        # 检查日志文件
        time.sleep(1)
        check_log_files()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_multithreading():
    """测试多线程环境下的日志记录"""
    print("\n📝 测试多线程日志记录...")
    
    def worker_thread(thread_id):
        from core.logger import log
        for i in range(10):
            log.info(f"线程 {thread_id} - 消息 {i}")
            if i % 3 == 0:
                log.error(f"线程 {thread_id} - 错误 {i}")
            time.sleep(0.01)  # 短暂延迟
    
    # 创建多个线程
    threads = []
    for i in range(5):
        thread = threading.Thread(target=worker_thread, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print("✅ 多线程测试完成")

def test_bulk_logging():
    """测试大量日志记录"""
    print("\n📝 测试大量日志记录...")
    
    from core.logger import log
    
    start_time = time.time()
    
    # 快速记录大量日志
    for i in range(100):
        log.info(f"批量日志 {i}")
        if i % 10 == 0:
            log.error(f"批量错误 {i}")
        if i % 20 == 0:
            log.debug(f"批量调试 {i}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"✅ 记录100条日志耗时: {duration:.3f}秒")

def check_log_files():
    """检查日志文件"""
    print("\n" + "=" * 30)
    print("检查日志文件")
    print("=" * 30)
    
    today = datetime.now().strftime("%Y%m%d")
    log_files = {
        "通用日志": f"logs/general/app_{today}.log",
        "调试日志": f"logs/debug/debug_{today}.log",
        "错误日志": f"logs/error/error_{today}.log"
    }
    
    total_size = 0
    for name, log_file in log_files.items():
        full_path = os.path.join(project_root, log_file)
        print(f"\n📁 {name}:")
        
        if os.path.exists(full_path):
            size = os.path.getsize(full_path)
            total_size += size
            print(f"  ✅ 文件大小: {size} 字节")
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print(f"  📄 共 {len(lines)} 行日志")
                    
                    # 显示最后几行
                    for line in lines[-2:]:
                        if line.strip():
                            print(f"    {line.strip()}")
                            
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
        else:
            print(f"  ❌ 文件不存在")
    
    print(f"\n📊 总日志大小: {total_size} 字节")

def test_edge_cases():
    """测试边缘情况"""
    print("\n📝 测试边缘情况...")
    
    from core.logger import log
    
    # 测试特殊字符
    log.info("测试特殊字符: 中文、emoji 🚀、符号 @#$%")
    
    # 测试长消息
    long_message = "这是一条很长的日志消息 " * 50
    log.info(long_message)
    
    # 测试空消息
    log.info("")
    
    # 测试None值
    try:
        log.info(None)
    except Exception:
        pass
    
    print("✅ 边缘情况测试完成")

if __name__ == "__main__":
    test_ultra_safe_logger()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("超级安全日志测试完成")
    print("=" * 50)
