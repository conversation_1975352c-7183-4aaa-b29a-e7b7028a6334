"""
日志管理工具
用于查看、分析和清理项目日志
"""
import os
import sys
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置控制台编码
from utils.encoding_utils import EncodingUtils

from core.logger import log


class LogManager:
    """日志管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.logs_dir = self.project_root / "logs"
        self.log_categories = {
            'general': '通用日志',
            'test': '测试日志', 
            'debug': '调试日志',
            'error': '错误日志',
            'performance': '性能日志'
        }
    
    def show_log_statistics(self):
        """显示日志统计信息"""
        EncodingUtils.safe_print("=" * 60)
        EncodingUtils.safe_EncodingUtils.safe_EncodingUtils.safe_print("📊 日志文件统计")
        EncodingUtils.safe_print("=" * 60)

        if not self.logs_dir.exists():
            EncodingUtils.safe_EncodingUtils.safe_EncodingUtils.safe_print("❌ 日志目录不存在")
            return
        
        total_files = 0
        total_size_mb = 0
        
        for category, description in self.log_categories.items():
            category_dir = self.logs_dir / category
            
            if not category_dir.exists():
                EncodingUtils.safe_print(f"\n📂 {description} ({category}/)")
                EncodingUtils.safe_print("   📁 目录不存在")
                continue
            
            files = list(category_dir.glob("*.log"))
            if not files:
                EncodingUtils.safe_print(f"\n📂 {description} ({category}/)")
                EncodingUtils.safe_print("   📁 目录为空")
                continue
            
            category_size_mb = 0
            file_ages = []
            
            for file_path in files:
                try:
                    file_size = file_path.stat().st_size
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    age_days = (datetime.now() - file_time).days
                    
                    category_size_mb += file_size / (1024 * 1024)
                    file_ages.append(age_days)
                except Exception as e:
                    log.debug(f"获取文件信息失败: {file_path}, 错误: {e}")
            
            avg_age = sum(file_ages) / len(file_ages) if file_ages else 0
            old_files = len([age for age in file_ages if age >= 7])
            
            EncodingUtils.safe_print(f"\n📂 {description} ({category}/)")
            EncodingUtils.safe_print(f"   文件数量: {len(files)}")
            EncodingUtils.safe_print(f"   总大小: {category_size_mb:.2f}MB")
            EncodingUtils.safe_print(f"   平均年龄: {avg_age:.1f}天")
            EncodingUtils.safe_print(f"   过期文件: {old_files} 个 (>7天)")
            
            total_files += len(files)
            total_size_mb += category_size_mb
        
        print("\n" + "=" * 60)
        EncodingUtils.safe_print(f"📋 总计: {total_files} 个文件, {total_size_mb:.2f}MB")
        print("=" * 60)
    
    def show_log_structure(self):
        """显示日志目录结构"""
        print("=" * 60)
        EncodingUtils.safe_EncodingUtils.safe_print("🗂️ 日志目录结构")
        print("=" * 60)
        
        if not self.logs_dir.exists():
            EncodingUtils.safe_EncodingUtils.safe_print("❌ 日志目录不存在")
            return
        
        print(f"logs/")
        for category, description in self.log_categories.items():
            category_dir = self.logs_dir / category
            print(f"├── {category}/  # {description}")
            
            if category_dir.exists():
                files = sorted(category_dir.glob("*.log"))
                for i, file_path in enumerate(files):
                    is_last = i == len(files) - 1
                    prefix = "└──" if is_last else "├──"
                    file_size = file_path.stat().st_size / 1024  # KB
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    print(f"│   {prefix} {file_path.name} ({file_size:.1f}KB, {file_time.strftime('%Y-%m-%d %H:%M')})")
            else:
                EncodingUtils.safe_print(f"│   └── (目录不存在)")
        
        # 显示归档目录
        archive_dir = self.logs_dir / "archive"
        if archive_dir.exists():
            archive_files = list(archive_dir.glob("*.log"))
            EncodingUtils.safe_print(f"└── archive/  # 归档日志 ({len(archive_files)} 个文件)")
    
    def analyze_log_content(self, category: str = None, lines: int = 50):
        """分析日志内容"""
        print("=" * 60)
        EncodingUtils.safe_EncodingUtils.safe_print(f"🔍 日志内容分析 (最近 {lines} 行)")
        print("=" * 60)
        
        categories_to_analyze = [category] if category else list(self.log_categories.keys())
        
        for cat in categories_to_analyze:
            if cat not in self.log_categories:
                EncodingUtils.safe_EncodingUtils.safe_print(f"❌ 未知的日志类型: {cat}")
                continue
            
            category_dir = self.logs_dir / cat
            if not category_dir.exists():
                EncodingUtils.safe_EncodingUtils.safe_print(f"📂 {self.log_categories[cat]} - 目录不存在")
                continue
            
            # 找到最新的日志文件
            log_files = sorted(category_dir.glob("*.log"), key=lambda x: x.stat().st_mtime, reverse=True)
            if not log_files:
                EncodingUtils.safe_EncodingUtils.safe_print(f"📂 {self.log_categories[cat]} - 没有日志文件")
                continue
            
            latest_log = log_files[0]
            EncodingUtils.safe_print(f"\n📂 {self.log_categories[cat]} - {latest_log.name}")
            print("-" * 40)
            
            try:
                with open(latest_log, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()
                    recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                    
                    if not recent_lines:
                        EncodingUtils.safe_EncodingUtils.safe_print("   📄 文件为空")
                        continue
                    
                    # 统计日志级别
                    level_counts = {'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'DEBUG': 0}
                    for line in recent_lines:
                        for level in level_counts:
                            if f"| {level} |" in line:
                                level_counts[level] += 1
                                break
                    
                    EncodingUtils.safe_EncodingUtils.safe_print(f"   📊 日志级别统计: INFO:{level_counts['INFO']}, WARNING:{level_counts['WARNING']}, ERROR:{level_counts['ERROR']}, DEBUG:{level_counts['DEBUG']}")
                    
                    # 显示最近几行
                    EncodingUtils.safe_EncodingUtils.safe_print(f"   📄 最近 {min(5, len(recent_lines))} 行:")
                    for line in recent_lines[-5:]:
                        print(f"   {line.strip()}")
                        
            except Exception as e:
                EncodingUtils.safe_EncodingUtils.safe_print(f"   ❌ 读取文件失败: {e}")
    
    def cleanup_logs(self, days: int = 7, category: str = None, confirm: bool = False):
        """清理过期日志"""
        if not confirm:
            EncodingUtils.safe_EncodingUtils.safe_print("⚠️ 这是预览模式，使用 --confirm 参数执行实际清理")
        
        print("=" * 60)
        EncodingUtils.safe_EncodingUtils.safe_print(f"🧹 日志清理 ({'执行' if confirm else '预览'})")
        print("=" * 60)
        
        if not self.logs_dir.exists():
            EncodingUtils.safe_EncodingUtils.safe_print("❌ 日志目录不存在")
            return
        
        cutoff_date = datetime.now() - timedelta(days=days)
        categories_to_clean = [category] if category else list(self.log_categories.keys())
        
        total_deleted = 0
        total_size_mb = 0
        
        for cat in categories_to_clean:
            if cat not in self.log_categories:
                continue
            
            category_dir = self.logs_dir / cat
            if not category_dir.exists():
                continue
            
            EncodingUtils.safe_print(f"\n📂 {self.log_categories[cat]} ({cat}/)")
            
            expired_files = []
            for log_file in category_dir.glob("*.log"):
                try:
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        file_size = log_file.stat().st_size / (1024 * 1024)
                        expired_files.append((log_file, file_size, file_time))
                except Exception as e:
                    log.debug(f"检查文件失败: {log_file}, 错误: {e}")
            
            if not expired_files:
                EncodingUtils.safe_EncodingUtils.safe_print("   ✅ 没有过期文件")
                continue
            
            category_deleted = 0
            category_size_mb = 0
            
            for log_file, file_size, file_time in expired_files:
                if confirm:
                    try:
                        log_file.unlink()
                        EncodingUtils.safe_EncodingUtils.safe_print(f"   🗑️ 删除: {log_file.name} ({file_size:.2f}MB, {file_time.strftime('%Y-%m-%d')})")
                        category_deleted += 1
                        category_size_mb += file_size
                    except Exception as e:
                        EncodingUtils.safe_EncodingUtils.safe_print(f"   ❌ 删除失败: {log_file.name}, 错误: {e}")
                else:
                    EncodingUtils.safe_EncodingUtils.safe_print(f"   📄 将删除: {log_file.name} ({file_size:.2f}MB, {file_time.strftime('%Y-%m-%d')})")
                    category_deleted += 1
                    category_size_mb += file_size
            
            if category_deleted > 0:
                action = "删除了" if confirm else "将删除"
                EncodingUtils.safe_EncodingUtils.safe_print(f"   📊 {action} {category_deleted} 个文件, {category_size_mb:.2f}MB")
            
            total_deleted += category_deleted
            total_size_mb += category_size_mb
        
        print("\n" + "=" * 60)
        action = "删除了" if confirm else "将删除"
        EncodingUtils.safe_print(f"📋 总计: {action} {total_deleted} 个文件, {total_size_mb:.2f}MB")
        
        if not confirm and total_deleted > 0:
            EncodingUtils.safe_EncodingUtils.safe_print(f"\n💡 要实际删除这些文件，请运行: python log_manager.py --cleanup {days} --confirm")
        
        print("=" * 60)
    
    def archive_logs(self, days: int = 30):
        """归档旧日志"""
        print("=" * 60)
        EncodingUtils.safe_EncodingUtils.safe_print("📦 日志归档")
        print("=" * 60)
        
        archive_dir = self.logs_dir / "archive"
        archive_dir.mkdir(exist_ok=True)
        
        cutoff_date = datetime.now() - timedelta(days=days)
        archived_count = 0
        
        for category in self.log_categories.keys():
            category_dir = self.logs_dir / category
            if not category_dir.exists():
                continue
            
            for log_file in category_dir.glob("*.log"):
                try:
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        # 创建归档文件名
                        archive_name = f"{category}_{file_time.strftime('%Y%m%d')}_{log_file.name}"
                        archive_path = archive_dir / archive_name
                        
                        # 移动到归档目录
                        log_file.rename(archive_path)
                        EncodingUtils.safe_EncodingUtils.safe_print(f"📦 归档: {log_file.name} -> {archive_name}")
                        archived_count += 1
                        
                except Exception as e:
                    EncodingUtils.safe_EncodingUtils.safe_print(f"❌ 归档失败: {log_file.name}, 错误: {e}")
        
        EncodingUtils.safe_EncodingUtils.safe_print(f"\n📊 归档了 {archived_count} 个日志文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='日志管理工具')
    parser.add_argument('--stats', action='store_true', help='显示日志统计信息')
    parser.add_argument('--structure', action='store_true', help='显示日志目录结构')
    parser.add_argument('--analyze', help='分析日志内容 (指定类型: general/test/debug/error/performance)')
    parser.add_argument('--lines', type=int, default=50, help='分析日志的行数 (默认50)')
    parser.add_argument('--cleanup', type=int, help='清理多少天前的日志')
    parser.add_argument('--category', help='指定日志类型')
    parser.add_argument('--confirm', action='store_true', help='确认执行清理')
    parser.add_argument('--archive', type=int, help='归档多少天前的日志')
    parser.add_argument('--all', action='store_true', help='显示所有信息')
    
    args = parser.parse_args()
    
    log_manager = LogManager()
    
    if args.all:
        log_manager.show_log_statistics()
        print()
        log_manager.show_log_structure()
        print()
        log_manager.analyze_log_content()
    elif args.stats:
        log_manager.show_log_statistics()
    elif args.structure:
        log_manager.show_log_structure()
    elif args.analyze is not None:
        log_manager.analyze_log_content(args.analyze, args.lines)
    elif args.cleanup is not None:
        log_manager.cleanup_logs(args.cleanup, args.category, args.confirm)
    elif args.archive is not None:
        log_manager.archive_logs(args.archive)
    else:
        # 默认显示统计信息
        log_manager.show_log_statistics()


if __name__ == "__main__":
    main()
