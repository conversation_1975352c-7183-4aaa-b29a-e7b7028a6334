"""
Ella浮窗页面使用示例
演示如何使用EllaFloatingPage类进行浮窗操作
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.ella.floating_page import EllaFloatingPage
from core.logger import log


def main():
    """主函数 - 演示浮窗页面的基本使用"""
    
    # 初始化浮窗页面
    floating_page = EllaFloatingPage()
    
    try:
        log.info("=== Ella浮窗页面使用示例 ===")
        
        # 1. 检查浮窗状态
        log.info("1. 检查浮窗当前状态")
        status = floating_page.check_floating_window_status()
        log.info(f"浮窗状态: {status}")
        
        # 2. 通过长按power键唤起浮窗
        log.info("2. 通过长按power键唤起Ella浮窗")
        if floating_page.trigger_ella_by_power_key(duration=3.0):
            log.info("✅ 通过长按power键成功唤起浮窗")
        else:
            log.warning("长按power键未能唤起浮窗，尝试其他方法")
            # 备选方案：使用通用打开方法
            if floating_page.open_floating_window():
                log.info("✅ 通过备选方法打开浮窗成功")
            else:
                log.error("❌ 所有方法都无法打开浮窗")
                return
        
        # 3. 确保浮窗就绪
        log.info("3. 确保浮窗就绪")
        if floating_page.ensure_floating_window_ready():
            log.info("✅ 浮窗已就绪")
        else:
            log.error("❌ 浮窗未就绪")
            return
        
        # 4. 执行文本命令
        log.info("4. 在浮窗中执行文本命令")
        test_command = "What's the weather today?"
        if floating_page.execute_text_command_in_floating(test_command):
            log.info("✅ 文本命令执行成功")
            
            # 等待响应
            if floating_page.wait_for_floating_response(timeout=10):
                response = floating_page.get_floating_response_text()
                log.info(f"AI响应: {response}")
            else:
                log.warning("未收到AI响应")
        else:
            log.error("❌ 文本命令执行失败")
        
        # 5. 移动浮窗位置
        log.info("5. 移动浮窗位置")
        if floating_page.move_floating_window(500, 300):
            log.info("✅ 浮窗移动成功")
        else:
            log.warning("浮窗移动失败")
        
        # 6. 最小化浮窗
        log.info("6. 最小化浮窗")
        if floating_page.minimize_floating_window():
            log.info("✅ 浮窗最小化成功")
            time.sleep(2)
        else:
            log.warning("浮窗最小化失败")
        
        # 7. 展开浮窗
        log.info("7. 展开浮窗")
        if floating_page.expand_floating_window():
            log.info("✅ 浮窗展开成功")
        else:
            log.warning("浮窗展开失败")
        
        # 8. 获取所有响应文本
        log.info("8. 获取所有响应文本")
        all_texts = floating_page.get_all_floating_response_texts()
        log.info(f"所有响应文本: {all_texts}")
        
        # 9. 关闭浮窗
        log.info("9. 关闭浮窗")
        if floating_page.close_floating_window():
            log.info("✅ 浮窗关闭成功")
        else:
            log.error("❌ 浮窗关闭失败")
        
        log.info("=== 示例执行完成 ===")
        
    except Exception as e:
        log.error(f"示例执行异常: {e}")
    
    finally:
        # 确保清理资源
        try:
            floating_page.close_floating_window()
        except:
            pass


def test_floating_window_operations():
    """测试浮窗的各种操作"""
    
    floating_page = EllaFloatingPage()
    
    test_cases = [
        {
            'name': '天气查询',
            'command': 'What is the weather like today?',
            'expected_keywords': ['weather', 'temperature', 'today']
        },
        {
            'name': '时间查询',
            'command': 'What time is it now?',
            'expected_keywords': ['time', 'now', 'current']
        },
        {
            'name': '简单问候',
            'command': 'Hello Ella',
            'expected_keywords': ['hello', 'hi', 'help']
        }
    ]
    
    log.info("=== 浮窗操作测试 ===")
    
    # 确保浮窗就绪
    if not floating_page.ensure_floating_window_ready():
        log.error("浮窗未就绪，测试终止")
        return
    
    for i, test_case in enumerate(test_cases, 1):
        log.info(f"测试 {i}: {test_case['name']}")
        
        # 执行命令
        if floating_page.execute_text_command_in_floating(test_case['command']):
            # 等待响应
            if floating_page.wait_for_floating_response():
                response = floating_page.get_floating_response_text()
                log.info(f"响应: {response}")
                
                # 验证响应
                response_lower = response.lower()
                found_keywords = [kw for kw in test_case['expected_keywords'] 
                                if kw.lower() in response_lower]
                
                if found_keywords:
                    log.info(f"✅ 测试通过，找到关键词: {found_keywords}")
                else:
                    log.warning(f"⚠️ 测试部分通过，未找到预期关键词")
            else:
                log.error(f"❌ 测试失败，未收到响应")
        else:
            log.error(f"❌ 测试失败，命令执行失败")
        
        # 测试间隔
        time.sleep(2)
    
    log.info("=== 测试完成 ===")


def test_power_key_trigger():
    """测试长按power键唤起Ella浮窗功能"""

    floating_page = EllaFloatingPage()

    log.info("=== 长按Power键唤起Ella浮窗测试 ===")

    # 测试不同的长按时长
    durations = [2.0, 3.0, 4.0, 5.0]

    for duration in durations:
        log.info(f"测试长按power键 {duration}秒")

        # 确保浮窗先关闭
        floating_page.close_floating_window()
        time.sleep(2)

        # 尝试通过长按power键唤起
        if floating_page.trigger_ella_by_power_key(duration=duration):
            log.info(f"✅ 长按{duration}秒成功唤起浮窗")

            # 测试基本功能
            if floating_page.execute_text_command_in_floating("Hello Ella"):
                if floating_page.wait_for_floating_response():
                    response = floating_page.get_floating_response_text()
                    log.info(f"响应: {response}")
                    log.info(f"✅ 长按{duration}秒唤起的浮窗功能正常")
                else:
                    log.warning(f"⚠️ 长按{duration}秒唤起的浮窗无响应")
            else:
                log.warning(f"⚠️ 长按{duration}秒唤起的浮窗无法执行命令")

            # 成功后退出测试
            break
        else:
            log.warning(f"❌ 长按{duration}秒未能唤起浮窗")

    log.info("=== 长按Power键测试完成 ===")


def test_different_trigger_methods():
    """测试不同的浮窗唤起方法"""

    floating_page = EllaFloatingPage()

    log.info("=== 不同浮窗唤起方法测试 ===")

    methods = [
        {
            'name': '长按Power键',
            'method': lambda: floating_page.trigger_ella_by_power_key(3.0)
        },
        {
            'name': '通用打开方法',
            'method': lambda: floating_page.open_floating_window()
        }
    ]

    for method_info in methods:
        log.info(f"测试方法: {method_info['name']}")

        # 确保浮窗先关闭
        floating_page.close_floating_window()
        time.sleep(2)

        # 尝试唤起浮窗
        if method_info['method']():
            log.info(f"✅ {method_info['name']} 成功")

            # 检查浮窗状态
            status = floating_page.check_floating_window_status()
            log.info(f"浮窗状态: {status}")

            # 测试基本交互
            if floating_page.execute_text_command_in_floating("Test command"):
                log.info(f"✅ {method_info['name']} 唤起的浮窗可以正常交互")
            else:
                log.warning(f"⚠️ {method_info['name']} 唤起的浮窗无法交互")

            break
        else:
            log.warning(f"❌ {method_info['name']} 失败")

    log.info("=== 不同唤起方法测试完成 ===")


if __name__ == '__main__':
    # 运行基本示例
    main()

    # 可选：运行测试用例
    # test_floating_window_operations()

    # 可选：测试长按power键功能
    # test_power_key_trigger()

    # 可选：测试不同唤起方法
    # test_different_trigger_methods()
