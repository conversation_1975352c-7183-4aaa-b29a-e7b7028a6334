"""
编码工具类
用于解决Windows系统下的中文乱码问题
"""
import sys
import os
import codecs
from typing import Optional


class EncodingUtils:
    """编码工具类"""
    
    @staticmethod
    def setup_console_encoding():
        """
        设置控制台编码为UTF-8
        主要解决Windows系统下的中文乱码问题
        """
        if sys.platform.startswith('win'):
            try:
                # 设置控制台代码页为UTF-8
                os.system('chcp 65001 > nul')
                
                # 重新包装stdout和stderr
                if hasattr(sys.stdout, 'detach'):
                    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
                if hasattr(sys.stderr, 'detach'):
                    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
                    
            except Exception:
                # 如果设置失败，静默处理
                pass
    
    @staticmethod
    def ensure_utf8_encoding(text: str) -> str:
        """
        确保文本为UTF-8编码
        
        Args:
            text: 输入文本
            
        Returns:
            str: UTF-8编码的文本
        """
        if isinstance(text, bytes):
            try:
                return text.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    return text.decode('gbk')
                except UnicodeDecodeError:
                    return text.decode('utf-8', errors='ignore')
        return text
    
    @staticmethod
    def safe_print(*args, **kwargs):
        """
        安全的打印函数，自动处理编码问题
        
        Args:
            *args: 打印参数
            **kwargs: 打印关键字参数
        """
        try:
            # 确保所有参数都是正确编码的字符串
            safe_args = []
            for arg in args:
                if isinstance(arg, (str, int, float, bool)):
                    safe_args.append(str(arg))
                else:
                    safe_args.append(EncodingUtils.ensure_utf8_encoding(str(arg)))
            
            print(*safe_args, **kwargs)
        except UnicodeEncodeError:
            # 如果仍然有编码错误，使用错误处理
            safe_args = []
            for arg in args:
                safe_args.append(str(arg).encode('utf-8', errors='ignore').decode('utf-8'))
            print(*safe_args, **kwargs)
    
    @staticmethod
    def get_system_encoding() -> str:
        """
        获取系统默认编码
        
        Returns:
            str: 系统编码名称
        """
        return sys.getdefaultencoding()
    
    @staticmethod
    def get_console_encoding() -> Optional[str]:
        """
        获取控制台编码
        
        Returns:
            Optional[str]: 控制台编码名称
        """
        try:
            if hasattr(sys.stdout, 'encoding'):
                return sys.stdout.encoding
            return None
        except Exception:
            return None
    
    @staticmethod
    def fix_subprocess_encoding(result: 'subprocess.CompletedProcess') -> 'subprocess.CompletedProcess':
        """
        修复subprocess结果的编码问题
        
        Args:
            result: subprocess执行结果
            
        Returns:
            subprocess.CompletedProcess: 修复编码后的结果
        """
        if result.stdout:
            result.stdout = EncodingUtils.ensure_utf8_encoding(result.stdout)
        if result.stderr:
            result.stderr = EncodingUtils.ensure_utf8_encoding(result.stderr)
        return result


# 自动设置控制台编码
EncodingUtils.setup_console_encoding()
