# 日志系统说明

## 概述

项目使用基于loguru的简化日志系统，支持不同级别的日志自动分类写入到对应文件。

## 功能特性

- **控制台输出**: 所有INFO及以上级别的日志都会在控制台显示
- **文件分类**: 根据日志级别自动写入到不同的文件
- **自动轮转**: 支持文件大小轮转和保留期限管理
- **简单易用**: 直接导入使用，无需复杂配置
- **稳定可靠**: 修复了缓冲区分离错误，支持多线程环境

## 日志文件分类

| 文件 | 级别 | 说明 |
|------|------|------|
| `logs/general/app_YYYYMMDD.log` | INFO及以上 | 通用日志，记录所有INFO、WARNING、ERROR |
| `logs/debug/debug_YYYYMMDD.log` | 所有级别 | 调试日志，记录所有级别的日志 |
| `logs/error/error_YYYYMMDD.log` | ERROR及以上 | 错误日志，只记录ERROR和CRITICAL |

## 使用方法

### 基本使用

```python
from core.logger import log

# 记录不同级别的日志
log.debug("调试信息")
log.info("普通信息")
log.warning("警告信息")
log.error("错误信息")
```

### 在测试中使用

```python
from core.logger import log

def test_something():
    log.info("开始测试")
    try:
        # 测试代码
        result = some_operation()
        log.info(f"测试成功: {result}")
    except Exception as e:
        log.error(f"测试失败: {e}")
```

## 配置说明

日志系统会自动：
- 创建必要的目录结构
- 按日期命名日志文件 (YYYYMMDD格式)
- 当文件大小超过限制时自动轮转
- 自动清理过期的日志文件

### 轮转和保留设置

- **通用日志**: 10MB轮转，保留10天
- **调试日志**: 5MB轮转，保留7天  
- **错误日志**: 5MB轮转，保留30天

## 注意事项

1. 日志系统使用单例模式，确保全局一致性
2. 所有日志文件使用UTF-8编码
3. 控制台输出支持颜色显示
4. 系统会自动处理异常，确保日志记录不影响主程序运行
5. 已修复缓冲区分离错误，在多线程环境下稳定运行
6. 使用stderr作为控制台输出，避免缓冲区问题

## 目录结构

```
logs/
├── general/          # 通用日志
├── debug/           # 调试日志
├── error/           # 错误日志
└── archive/         # 归档目录(预留)
```
