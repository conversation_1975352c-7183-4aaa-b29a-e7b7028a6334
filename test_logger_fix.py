#!/usr/bin/env python3
"""
测试日志修复 - 验证 "Record was:" 问题是否解决
"""
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def test_logger_fix():
    """测试日志修复"""
    print("=" * 60)
    print("测试日志修复 - 验证 'Record was:' 问题是否解决")
    print("=" * 60)
    
    try:
        # 导入日志模块
        from core.logger import log, Logger
        
        print("✅ 成功导入日志模块")
        
        # 测试基本日志记录
        print("\n📝 测试基本日志记录...")
        log.info("测试日志修复 - INFO级别")
        log.warning("测试日志修复 - WARNING级别")
        log.error("测试日志修复 - ERROR级别")
        log.debug("测试日志修复 - DEBUG级别")
        
        print("✅ 基本日志记录完成，检查是否有 'Record was:' 输出")
        
        # 测试安全日志方法
        print("\n📝 测试安全日志方法...")
        Logger.safe_log("info", "使用安全日志方法 - INFO")
        Logger.safe_log("error", "使用安全日志方法 - ERROR")
        Logger.safe_log("debug", "使用安全日志方法 - DEBUG")
        
        print("✅ 安全日志方法测试完成")
        
        # 测试异常情况
        print("\n📝 测试异常情况...")
        try:
            # 尝试记录可能导致问题的内容
            log.info("测试特殊字符: 中文、emoji 🚀、符号 @#$%")
            log.info(f"测试时间戳: {datetime.now()}")
            log.info("测试长消息: " + "很长的消息 " * 20)
        except Exception as e:
            print(f"⚠️ 异常情况测试中出现错误: {e}")
        
        print("✅ 异常情况测试完成")
        
        # 等待日志写入完成
        time.sleep(1)
        
        # 检查日志文件
        check_log_files()
        
        print("\n🎉 日志修复测试完成！")
        print("如果没有看到 'Record was:' 输出，说明问题已解决")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def check_log_files():
    """检查日志文件"""
    print("\n" + "=" * 40)
    print("检查日志文件")
    print("=" * 40)
    
    today = datetime.now().strftime("%Y%m%d")
    log_files = {
        "通用日志": f"logs/general/app_{today}.log",
        "调试日志": f"logs/debug/debug_{today}.log",
        "错误日志": f"logs/error/error_{today}.log"
    }
    
    for name, log_file in log_files.items():
        full_path = os.path.join(project_root, log_file)
        print(f"\n📁 {name} ({log_file}):")
        
        if os.path.exists(full_path):
            size = os.path.getsize(full_path)
            print(f"  ✅ 文件存在，大小: {size} 字节")
            
            if size > 0:
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        print(f"  📄 共 {len(lines)} 行日志")
                        
                        # 检查是否有 "Record was:" 输出
                        record_was_found = False
                        for line in lines:
                            if "Record was:" in line:
                                record_was_found = True
                                print(f"  ⚠️ 发现 'Record was:' 输出: {line.strip()}")
                        
                        if not record_was_found:
                            print(f"  ✅ 未发现 'Record was:' 输出")
                        
                        # 显示最后几行
                        print(f"  📝 最后2行内容:")
                        for line in lines[-2:]:
                            if line.strip():
                                print(f"    {line.strip()}")
                                
                except Exception as e:
                    print(f"  ❌ 读取文件失败: {e}")
            else:
                print(f"  ⚠️ 文件为空")
        else:
            print(f"  ❌ 文件不存在")


def test_base_page_logging():
    """测试 BasePage 的日志记录"""
    print("\n" + "=" * 40)
    print("测试 BasePage 日志记录")
    print("=" * 40)
    
    try:
        # 模拟 BasePage 的初始化
        from core.logger import log
        
        app_name = "ella"
        page_name = "floating_page"
        
        print(f"模拟 BasePage 初始化...")
        log.info(f"初始化页面: {app_name} - {page_name}")
        
        print("✅ BasePage 日志记录测试完成")
        
    except Exception as e:
        print(f"❌ BasePage 日志测试失败: {e}")


if __name__ == "__main__":
    test_logger_fix()
    test_base_page_logging()
    
    print("\n" + "=" * 60)
    print("日志修复验证完成")
    print("=" * 60)
