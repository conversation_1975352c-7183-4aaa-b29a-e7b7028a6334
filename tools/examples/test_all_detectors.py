from utils.encoding_utils import EncodingUtils
"""
测试所有检测器脚本
验证所有Component检测器是否正常工作
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from pages.base.app_detector import AppDetector, AppType

def test_all_detectors():
    """测试所有检测器"""
    EncodingUtils.safe_EncodingUtils.safe_print("🔍 开始测试所有检测器...")
    print("=" * 60)
    
    try:
        # 初始化检测器
        detector = AppDetector()
        total_detectors = len(detector._detectors)
        EncodingUtils.safe_EncodingUtils.safe_print(f"✅ 成功初始化AppDetector，共加载 {total_detectors} 个检测器")
        print()
        
        # 统计信息
        success_count = 0
        error_count = 0
        
        # 测试每个应用类型
        for app_type in AppType:
            try:
                detector_instance = detector.get_detector(app_type)
                if detector_instance:
                    # 获取包名和关键词
                    package_names = detector_instance.get_package_names()
                    keywords = detector_instance.get_keywords()
                    
                    # 验证数据有效性
                    if package_names and keywords:
                        EncodingUtils.safe_EncodingUtils.safe_print(f"✅ {app_type.value:25} | 包名: {len(package_names):2}个 | 关键词: {len(keywords):2}个")
                        success_count += 1
                    else:
                        EncodingUtils.safe_EncodingUtils.safe_print(f"⚠️  {app_type.value:25} | 包名或关键词为空")
                        error_count += 1
                else:
                    EncodingUtils.safe_EncodingUtils.safe_print(f"❌ {app_type.value:25} | 检测器未找到")
                    error_count += 1
                    
            except Exception as e:
                EncodingUtils.safe_EncodingUtils.safe_print(f"❌ {app_type.value:25} | 错误: {str(e)[:30]}...")
                error_count += 1
        
        print()
        print("=" * 60)
        EncodingUtils.safe_EncodingUtils.safe_print("📊 测试结果统计:")
        EncodingUtils.safe_print(f"  总检测器数量: {total_detectors}")
        EncodingUtils.safe_print(f"  成功测试: {success_count}")
        EncodingUtils.safe_print(f"  失败/错误: {error_count}")
        EncodingUtils.safe_print(f"  成功率: {success_count/total_detectors*100:.1f}%")
        
        if error_count == 0:
            EncodingUtils.safe_print("\n🎉 所有检测器测试通过！")
        else:
            EncodingUtils.safe_EncodingUtils.safe_print(f"\n⚠️  有 {error_count} 个检测器存在问题，请检查。")
            
    except Exception as e:
        EncodingUtils.safe_EncodingUtils.safe_print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def show_detector_details():
    """显示检测器详细信息"""
    EncodingUtils.safe_EncodingUtils.safe_print("\n🔍 检测器详细信息:")
    print("=" * 80)
    
    try:
        detector = AppDetector()
        
        # 按分类显示
        categories = {
            "原有检测器": ["weather", "camera", "settings", "contacts", "facebook", "music", "clock", "maps", "playstore"],
            "通信类": ["dialer", "smart_message", "google_contact", "google_phone", "google_message"],
            "AI智能助手": ["ella", "mol", "ai_core_model_manager", "ella_screen_recognition", "micro_intelligence"],
            "工具类": ["recorder", "phone_master", "notes", "smart_switch", "file_manager", "smart_scanner"],
            "娱乐媒体": ["ai_gallery", "clipper", "vishaplayer", "flipmusic", "calendar", "fm_radio"],
            "系统界面": ["launcher", "theme", "wallpaper", "system_ui", "global_search", "zero_screen"],
        }
        
        for category, app_types in categories.items():
            EncodingUtils.safe_print(f"\n📂 {category}:")
            for app_type_str in app_types:
                try:
                    app_type = AppType(app_type_str)
                    detector_instance = detector.get_detector(app_type)
                    if detector_instance:
                        package_names = detector_instance.get_package_names()
                        keywords = detector_instance.get_keywords()
                        EncodingUtils.safe_print(f"  {app_type_str:20} | 包名: {package_names[0] if package_names else '无'}")
                        EncodingUtils.safe_print(f"  {' '*20} | 关键词: {', '.join(keywords[:3]) if keywords else '无'}")
                except:
                    continue
                    
    except Exception as e:
        EncodingUtils.safe_EncodingUtils.safe_print(f"❌ 显示详细信息失败: {e}")

if __name__ == "__main__":
    # 运行测试
    test_all_detectors()
    
    # 询问是否显示详细信息
    if len(sys.argv) > 1 and sys.argv[1] == "--details":
        show_detector_details()
    else:
        EncodingUtils.safe_EncodingUtils.safe_print("\n💡 提示: 使用 'python tools/test_all_detectors.py --details' 查看详细信息")
