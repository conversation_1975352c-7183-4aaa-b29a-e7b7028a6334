"""
修复编码问题的脚本
批量替换项目中的print函数为安全的打印函数
"""
import os
import re
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from utils.encoding_utils import EncodingUtils


def fix_print_statements_in_file(file_path: str) -> bool:
    """
    修复文件中的print语句
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否有修改
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 检查是否已经导入了EncodingUtils
        if 'from utils.encoding_utils import EncodingUtils' not in content:
            # 在适当位置添加导入
            if 'import' in content:
                # 找到最后一个import语句的位置
                import_lines = []
                other_lines = []
                in_imports = True
                
                for line in content.split('\n'):
                    if in_imports and (line.startswith('import ') or line.startswith('from ') or line.strip() == '' or line.startswith('#')):
                        import_lines.append(line)
                    else:
                        if in_imports:
                            import_lines.append('from utils.encoding_utils import EncodingUtils')
                            in_imports = False
                        other_lines.append(line)
                
                if in_imports:  # 如果文件只有import语句
                    import_lines.append('from utils.encoding_utils import EncodingUtils')
                
                content = '\n'.join(import_lines + other_lines)
        
        # 替换print函数调用
        # 匹配包含中文字符的print语句
        chinese_pattern = r'print\(([^)]*[\u4e00-\u9fff][^)]*)\)'
        content = re.sub(chinese_pattern, r'EncodingUtils.safe_print(\1)', content)
        
        # 替换其他可能的print语句（包含emoji等特殊字符）
        emoji_pattern = r'print\(([^)]*[📊🔍❌✅💡🗑️📄📂📦🧹⚠️][^)]*)\)'
        content = re.sub(emoji_pattern, r'EncodingUtils.safe_print(\1)', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        EncodingUtils.safe_print(f"处理文件 {file_path} 时出错: {e}")
        return False


def main():
    """主函数"""
    project_root = Path(__file__).parent.parent
    
    # 需要处理的文件列表
    files_to_fix = [
        'log_manager.py',
        'tools/adb_process_monitor.py',
        'tools/examples/test_all_detectors.py',
        'tools/debug_tools/install_allure.py'
    ]
    
    EncodingUtils.safe_print("🔧 开始修复编码问题...")
    
    fixed_count = 0
    for file_path in files_to_fix:
        full_path = project_root / file_path
        if full_path.exists():
            if fix_print_statements_in_file(str(full_path)):
                EncodingUtils.safe_print(f"✅ 已修复: {file_path}")
                fixed_count += 1
            else:
                EncodingUtils.safe_print(f"⏭️ 无需修复: {file_path}")
        else:
            EncodingUtils.safe_print(f"❌ 文件不存在: {file_path}")
    
    EncodingUtils.safe_print(f"\n📊 修复完成，共处理 {fixed_count} 个文件")


if __name__ == "__main__":
    main()
